#!/usr/bin/env python3
"""
Syntax-only test for the traditional_all mode implementation.
This script only tests syntax and basic structure without importing dependencies.
"""

import ast
import sys
import os

def test_syntax_validity():
    """Test that all Python files have valid syntax."""
    files_to_test = [
        'vibroml/auto_optimize.py',
        'vibroml/utils/utils.py'
    ]
    
    all_valid = True
    
    for file_path in files_to_test:
        try:
            with open(file_path, 'r') as f:
                source = f.read()
            
            # Parse the AST to check syntax
            ast.parse(source)
            print(f"✓ {file_path}: Syntax is valid")
            
        except SyntaxError as e:
            print(f"✗ {file_path}: Syntax error at line {e.lineno}: {e.msg}")
            all_valid = False
        except Exception as e:
            print(f"✗ {file_path}: Error reading file: {e}")
            all_valid = False
    
    return all_valid

def test_function_definitions():
    """Test that required functions are defined in the files."""
    try:
        with open('vibroml/auto_optimize.py', 'r') as f:
            content = f.read()

        required_functions = [
            'run_traditional_all_soft_mode_optimization',
            'identify_all_soft_modes_from_phonon_analysis',
            'generate_soft_mode_pairings'
        ]

        all_found = True
        for func_name in required_functions:
            if f"def {func_name}" in content:
                print(f"✓ Function '{func_name}' is defined")
            else:
                print(f"✗ Function '{func_name}' is missing")
                all_found = False

        return all_found

    except Exception as e:
        print(f"✗ Error checking function definitions: {e}")
        return False

def test_mode_swapping_implementation():
    """Test that mode swapping features are implemented."""
    try:
        with open('vibroml/auto_optimize.py', 'r') as f:
            content = f.read()

        mode_swapping_features = [
            'configurations = [',  # Configuration array
            "'modes_info': [softest_mode, other_mode]",  # Original configuration
            "'modes_info': [other_mode, softest_mode]",  # Swapped configuration
            'Original: {softest_mode',  # Original description
            'Swapped: {other_mode',  # Swapped description
            'config_sample_results',  # Configuration-specific results
            'Mode swapping enabled'  # Console output
        ]

        all_found = True
        for feature in mode_swapping_features:
            if feature in content:
                print(f"✓ Mode swapping feature found: '{feature[:30]}...'")
            else:
                print(f"✗ Mode swapping feature missing: '{feature[:30]}...'")
                all_found = False

        return all_found

    except Exception as e:
        print(f"✗ Error checking mode swapping implementation: {e}")
        return False

def test_method_choices():
    """Test that traditional_all is added to method choices."""
    try:
        with open('vibroml/utils/utils.py', 'r') as f:
            content = f.read()
        
        # Check if traditional_all is in the choices
        if 'choices=["ga", "traditional", "traditional_all"]' in content:
            print("✓ traditional_all method is added to argument parser choices")
            return True
        else:
            print("✗ traditional_all method is not found in argument parser choices")
            return False
            
    except Exception as e:
        print(f"✗ Error checking method choices: {e}")
        return False

def test_routing_logic():
    """Test that routing logic includes traditional_all."""
    try:
        with open('vibroml/auto_optimize.py', 'r') as f:
            content = f.read()
        
        # Check if traditional_all routing is present
        if 'elif args.method == "traditional_all":' in content:
            print("✓ traditional_all routing logic is present")
            return True
        else:
            print("✗ traditional_all routing logic is missing")
            return False
            
    except Exception as e:
        print(f"✗ Error checking routing logic: {e}")
        return False

def main():
    """Run all syntax tests."""
    print("Testing traditional_all mode implementation (syntax only)...")
    print("=" * 60)
    
    tests = [
        ("Syntax Validity", test_syntax_validity),
        ("Function Definitions", test_function_definitions),
        ("Mode Swapping Implementation", test_mode_swapping_implementation),
        ("Method Choices", test_method_choices),
        ("Routing Logic", test_routing_logic),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\nRunning {test_name} test...")
        if test_func():
            passed += 1
        else:
            print(f"Test {test_name} failed!")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All syntax tests passed! The traditional_all mode implementation structure is correct.")
        print("\nNext steps:")
        print("1. Test with actual VibroML environment: conda activate /globalscratch/ucl/modl/rgouvea/vibroml_env")
        print("2. Run with a test structure: python -m vibroml.main --cif test.cif --method traditional_all --auto")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
