#!/usr/bin/env python3
"""
Test script for the traditional_all mode implementation.
This script tests the argument parsing and basic functionality without running full calculations.
"""

import sys
import os

# Add the vibroml module to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def test_argument_parsing():
    """Test that the traditional_all method is accepted in argument parsing."""
    try:
        from vibroml.utils.utils import get_arg_parser_and_settings
        
        parser, settings = get_arg_parser_and_settings()
        
        # Test parsing with traditional_all method
        test_args = ['--cif', 'test.cif', '--method', 'traditional_all']
        args = parser.parse_args(test_args)
        
        assert args.method == 'traditional_all', f"Expected 'traditional_all', got '{args.method}'"
        print("✓ Argument parsing test passed: traditional_all method accepted")
        
        # Test that all three methods are accepted
        for method in ['ga', 'traditional', 'traditional_all']:
            test_args = ['--cif', 'test.cif', '--method', method]
            args = parser.parse_args(test_args)
            assert args.method == method, f"Expected '{method}', got '{args.method}'"
        
        print("✓ All methods (ga, traditional, traditional_all) are accepted")
        return True
        
    except Exception as e:
        print(f"✗ Argument parsing test failed: {e}")
        return False

def test_helper_functions():
    """Test the helper functions for soft mode identification and pairing."""
    try:
        from vibroml.auto_optimize import identify_all_soft_modes_from_phonon_analysis, generate_soft_mode_pairings
        
        # Test soft mode identification with mock data
        mock_softest_modes = [
            {'frequency': -0.5, 'label': 'Gamma', 'coordinate': [0, 0, 0]},
            {'frequency': -0.3, 'label': 'X', 'coordinate': [0.5, 0, 0]}
        ]
        
        mock_tracked_data = {
            'soft_modes': [
                {'frequency': -0.5, 'label': 'Gamma', 'coordinate': [0, 0, 0]},
                {'frequency': -0.3, 'label': 'X', 'coordinate': [0.5, 0, 0]},
                {'frequency': -0.2, 'label': 'Y', 'coordinate': [0, 0.5, 0]}
            ]
        }
        
        # Test identification function
        all_soft_modes = identify_all_soft_modes_from_phonon_analysis(
            mock_softest_modes, mock_tracked_data, -0.1
        )
        
        assert len(all_soft_modes) == 3, f"Expected 3 soft modes, got {len(all_soft_modes)}"
        assert all_soft_modes[0]['frequency'] == -0.5, "Softest mode should be first"
        print("✓ Soft mode identification test passed")
        
        # Test pairing function
        pairings = generate_soft_mode_pairings(all_soft_modes)
        
        assert len(pairings) == 2, f"Expected 2 pairings, got {len(pairings)}"
        assert pairings[0][0]['frequency'] == -0.5, "First mode in pairing should be softest"
        assert pairings[0][1]['frequency'] == -0.3, "Second mode in first pairing should be X"
        assert pairings[1][1]['frequency'] == -0.2, "Second mode in second pairing should be Y"
        print("✓ Soft mode pairing test passed")
        
        # Test edge case: less than 2 modes
        single_mode = [{'frequency': -0.5, 'label': 'Gamma', 'coordinate': [0, 0, 0]}]
        pairings_single = generate_soft_mode_pairings(single_mode)
        assert len(pairings_single) == 0, "Should return empty list for single mode"
        print("✓ Edge case test passed: single mode returns no pairings")
        
        return True
        
    except Exception as e:
        print(f"✗ Helper functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_function_import():
    """Test that the main function can be imported."""
    try:
        from vibroml.auto_optimize import run_traditional_all_soft_mode_optimization
        print("✓ Main function import test passed")
        return True
    except Exception as e:
        print(f"✗ Function import test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing traditional_all mode implementation...")
    print("=" * 50)
    
    tests = [
        ("Function Import", test_function_import),
        ("Argument Parsing", test_argument_parsing),
        ("Helper Functions", test_helper_functions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\nRunning {test_name} test...")
        if test_func():
            passed += 1
        else:
            print(f"Test {test_name} failed!")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The traditional_all mode implementation is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
